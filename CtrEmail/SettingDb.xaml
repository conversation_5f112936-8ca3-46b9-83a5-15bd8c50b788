<UserControl x:Class="CtrEmail.SettingDb"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:CtrEmail"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <Border CornerRadius="15,15,15,15">

        <Grid Margin="50">

            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
                <RowDefinition Height="40"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>

                <ColumnDefinition Width="*"/>

                <ColumnDefinition Width="150"/>

                <ColumnDefinition Width="2*"/>

                <ColumnDefinition Width="*" />

            </Grid.ColumnDefinitions>

            <TextBlock Grid.Row="0" Grid.Column="1" TextAlignment="Center"  VerticalAlignment="Center" HorizontalAlignment="Right" Text="服务器" Foreground="DarkBlue" Margin="0,0,20,0"/>
            <TextBox Grid.Row="0" Grid.Column="2" Name="ipBox" Margin="3" FontSize="17" />

            <TextBlock Grid.Row="1" Grid.Column="1" Text="验证方式" TextAlignment="Center" VerticalAlignment="Center" HorizontalAlignment="Right" Foreground="DarkBlue" Margin="0,0,20,0"/>
            <ComboBox Grid.Row="1" Grid.Column="2" Name="authCombo" SelectionChanged="onAuthCombo"/>

            <TextBlock Grid.Row="2" Grid.Column="1" Name="usernameLab" TextAlignment="Center"  VerticalAlignment="Center" HorizontalAlignment="Right" Text="用户名" Foreground="DarkBlue" Margin="0,0,20,0"/>
            <TextBox Grid.Row="2" Grid.Column="2" Name="usernameBox" Margin="3"/>

            <TextBlock Grid.Row="3" Grid.Column="1" Name="passwordLab" TextAlignment="Center" VerticalAlignment="Center" HorizontalAlignment="Right" Text="密码" Foreground="DarkBlue" Visibility="Hidden" Margin="0,0,20,0"/>
            <PasswordBox Grid.Row="3" Grid.Column="2" Name="passwordBox" Margin="3" Visibility="Hidden"/>
            

            <StackPanel Grid.Row="11" Grid.Column="1" Grid.ColumnSpan="2" Orientation="Vertical" VerticalAlignment="Center">

                <Button Content="确 定" Name="okBtn"  HorizontalAlignment="Center" VerticalAlignment="Center"   Click="onOkBtn"/>
            </StackPanel>

        </Grid>
    </Border>
    
</UserControl>
