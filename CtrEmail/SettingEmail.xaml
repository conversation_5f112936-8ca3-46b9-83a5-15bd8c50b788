<UserControl x:Class="CtrEmail.SettingEmail"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:local="clr-namespace:CtrEmail"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">
    <Border CornerRadius="15,15,15,15">
        <Grid Margin="50">
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/>
                <RowDefinition Height="30"/>
                <RowDefinition Height="30"/>
                <RowDefinition Height="30"/>
                <RowDefinition Height="30"/>
                <RowDefinition Height="30"/>
                <RowDefinition Height="30"/>
                <RowDefinition Height="30"/>
                <RowDefinition Height="30"/>
                <RowDefinition Height="30"/>
                <RowDefinition Height="30"/>
                <RowDefinition Height="50"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="260"/>
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <!-- 标题 -->
            <TextBlock Grid.Row="0" Grid.Column="1" Grid.ColumnSpan="2" TextAlignment="Center" VerticalAlignment="Center"
                       Text="SMTP邮件配置" FontSize="18" FontWeight="Bold" Foreground="DarkBlue"/>

            <!-- 发送方式选择 -->
            <TextBlock Grid.Row="1" Grid.Column="1" TextAlignment="Center" VerticalAlignment="Center" HorizontalAlignment="Right"
                       Text="发送方式" Foreground="DarkBlue"/>
            <StackPanel Grid.Row="1" Grid.Column="2" Orientation="Horizontal" Margin="20,0,0,0">
                <RadioButton Name="apiRadio" Content="API接口" IsChecked="True" Margin="0,0,20,0" Checked="OnSendModeChanged"/>
                <RadioButton Name="smtpRadio" Content="SMTP协议" Checked="OnSendModeChanged"/>
            </StackPanel>

            <!-- API配置区域 -->
            <TextBlock Grid.Row="2" Grid.Column="1" Name="lblApiServer" TextAlignment="Center" VerticalAlignment="Center"
                       HorizontalAlignment="Right" Text="API服务器" Foreground="DarkBlue" Visibility="Visible"/>
            <TextBox Grid.Row="2" Grid.Column="2" Name="apiServerBox" Margin="20,0,0,0"
                     Text="https://oudataservice.wuxiapptec.com/MailService/api/EmailSender/SendEmail"
                     IsReadOnly="True" Visibility="Visible"/>

            <!-- SMTP配置区域 -->
            <TextBlock Grid.Row="3" Grid.Column="1" Name="lblSmtpServer" TextAlignment="Center" VerticalAlignment="Center"
                       HorizontalAlignment="Right" Text="SMTP服务器" Foreground="DarkBlue" Visibility="Collapsed"/>
            <TextBox Grid.Row="3" Grid.Column="2" Name="smtpServerBox" Margin="20,0,0,0" Visibility="Collapsed"/>

            <TextBlock Grid.Row="4" Grid.Column="1" Name="lblSmtpPort" TextAlignment="Center" VerticalAlignment="Center"
                       HorizontalAlignment="Right" Text="SMTP端口" Foreground="DarkBlue" Visibility="Collapsed"/>
            <TextBox Grid.Row="4" Grid.Column="2" Name="smtpPortBox" Margin="20,0,0,0" Text="587" Visibility="Collapsed"/>

            <TextBlock Grid.Row="5" Grid.Column="1" Name="lblSmtpUsername" TextAlignment="Center" VerticalAlignment="Center"
                       HorizontalAlignment="Right" Text="SMTP用户名" Foreground="DarkBlue" Visibility="Collapsed"/>
            <TextBox Grid.Row="5" Grid.Column="2" Name="smtpUsernameBox" Margin="20,0,0,0" Visibility="Collapsed"/>

            <TextBlock Grid.Row="6" Grid.Column="1" Name="lblSmtpPassword" TextAlignment="Center" VerticalAlignment="Center"
                       HorizontalAlignment="Right" Text="SMTP密码" Foreground="DarkBlue" Visibility="Collapsed"/>
            <PasswordBox Grid.Row="6" Grid.Column="2" Name="smtpPasswordBox" Margin="20,0,0,0" Visibility="Collapsed"/>

            <TextBlock Grid.Row="7" Grid.Column="1" Name="lblSmtpSsl" TextAlignment="Center" VerticalAlignment="Center"
                       HorizontalAlignment="Right" Text="启用SSL" Foreground="DarkBlue" Visibility="Collapsed"/>
            <CheckBox Grid.Row="7" Grid.Column="2" Name="smtpSslCheckBox" Margin="20,0,0,0" IsChecked="True"
                      VerticalAlignment="Center" Visibility="Collapsed"/>

            <!-- 测试连接按钮 -->
            <Button Grid.Row="8" Grid.Column="2" Name="testConnectionBtn" Content="测试连接"
                    HorizontalAlignment="Left" Margin="20,0,0,0" Width="100" Click="OnTestConnection"/>

            <!-- 操作按钮 -->
            <StackPanel Grid.Row="11" Grid.ColumnSpan="4" Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="保存配置" Name="saveBtn" HorizontalAlignment="Center" VerticalAlignment="Center"
                        Margin="20,0,20,0" Width="100" Click="OnSaveConfig"/>
                <Button Content="重置配置" Name="resetBtn" HorizontalAlignment="Center" VerticalAlignment="Center"
                        Margin="20,0,20,0" Width="100" Click="OnResetConfig"/>
            </StackPanel>

        </Grid>
    </Border>
</UserControl>
