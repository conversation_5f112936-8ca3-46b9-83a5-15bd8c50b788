$content = Get-Content 'CtrEmail\CtrEmail.csproj'
$newContent = @()

foreach($line in $content) {
    $newContent += $line
    
    # 在SettingPara.xaml.cs的</Compile>后添加SettingEmail.xaml.cs
    if($line -match '</Compile>' -and $content[$content.IndexOf($line)-2] -match 'SettingPara.xaml.cs') {
        $newContent += '    <Compile Include="SettingEmail.xaml.cs">'
        $newContent += '      <DependentUpon>SettingEmail.xaml</DependentUpon>'
        $newContent += '    </Compile>'
    }
    
    # 在SettingPara.xaml的</Page>后添加SettingEmail.xaml
    if($line -match '</Page>' -and $content[$content.IndexOf($line)-2] -match 'SettingPara.xaml') {
        $newContent += '    <Page Include="SettingEmail.xaml">'
        $newContent += '      <SubType>Designer</SubType>'
        $newContent += '      <Generator>MSBuild:Compile</Generator>'
        $newContent += '    </Page>'
    }
}

Set-Content 'CtrEmail\CtrEmail.csproj' $newContent
