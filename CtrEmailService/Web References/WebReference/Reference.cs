//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

// 
// 此源代码是由 Microsoft.VSDesigner 4.0.30319.42000 版自动生成。
// 
#pragma warning disable 1591

namespace CtrEmailService.WebReference {
    using System;  asdf
    using System.Web.Services;
    using System.Diagnostics;
    using System.Web.Services.Protocols;
    using System.Xml.Serialization;
    using System.ComponentModel;
    
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    [System.Web.Services.WebServiceBindingAttribute(Name="ServiceBinding", Namespace="urn:wsdl")]
    public partial class wsdlService : System.Web.Services.Protocols.SoapHttpClientProtocol {
        
        private System.Threading.SendOrPostCallback loginOperationCompleted;
        
        private System.Threading.SendOrPostCallback getCtrsOperationCompleted;
        
        private System.Threading.SendOrPostCallback getYaoCtrsOperationCompleted;
        
        private System.Threading.SendOrPostCallback getStatusOperationCompleted;
        
        private System.Threading.SendOrPostCallback getCtrCardsOperationCompleted;
        
        private System.Threading.SendOrPostCallback getDbCardsOperationCompleted;
        
        private System.Threading.SendOrPostCallback setDbCardsOperationCompleted;
        
        private System.Threading.SendOrPostCallback getDoorsOperationCompleted;
        
        private System.Threading.SendOrPostCallback insertReserveCardsOperationCompleted;
        
        private System.Threading.SendOrPostCallback searchReserveCardsOperationCompleted;
        
        private System.Threading.SendOrPostCallback deleteReserveCardsOperationCompleted;
        
        private System.Threading.SendOrPostCallback insertPublicDoorsOperationCompleted;
        
        private System.Threading.SendOrPostCallback updatePublicDoorsOperationCompleted;
        
        private System.Threading.SendOrPostCallback searchPublicDoorsOperationCompleted;
        
        private System.Threading.SendOrPostCallback deletePublicDoorsOperationCompleted;
        
        private System.Threading.SendOrPostCallback updateDoorsOperationCompleted;
        
        private System.Threading.SendOrPostCallback insertEmployeesOperationCompleted;
        
        private System.Threading.SendOrPostCallback deleteEmployeesOperationCompleted;
        
        private System.Threading.SendOrPostCallback updateEmployeesOperationCompleted;
        
        private System.Threading.SendOrPostCallback searchEmployeesOperationCompleted;
        
        private System.Threading.SendOrPostCallback insertCardsOperationCompleted;
        
        private System.Threading.SendOrPostCallback updateCardsOperationCompleted;
        
        private System.Threading.SendOrPostCallback deleteCardsOperationCompleted;
        
        private System.Threading.SendOrPostCallback searchCardsOperationCompleted;
        
        private System.Threading.SendOrPostCallback insertDeptsOperationCompleted;
        
        private System.Threading.SendOrPostCallback updateDeptsOperationCompleted;
        
        private System.Threading.SendOrPostCallback deleteDeptsOperationCompleted;
        
        private System.Threading.SendOrPostCallback searchDeptsOperationCompleted;
        
        private System.Threading.SendOrPostCallback getCardsDoorsOperationCompleted;
        
        private System.Threading.SendOrPostCallback searchCardsDoorsOperationCompleted;
        
        private System.Threading.SendOrPostCallback asyncSetCardsDoorsOperationCompleted;
        
        private System.Threading.SendOrPostCallback asyncSetCardsDoorsXOperationCompleted;
        
        private System.Threading.SendOrPostCallback setCardsDoorsOperationCompleted;
        
        private System.Threading.SendOrPostCallback setCardsDoorsSyncOperationCompleted;
        
        private System.Threading.SendOrPostCallback normalCardeventReportOperationCompleted;
        
        private System.Threading.SendOrPostCallback abnormalCardeventReportOperationCompleted;
        
        private System.Threading.SendOrPostCallback doorRelationReportOperationCompleted;
        
        private System.Threading.SendOrPostCallback abnormalEmployeeeventReportOperationCompleted;
        
        private System.Threading.SendOrPostCallback employeeAuthReportOperationCompleted;
        
        private System.Threading.SendOrPostCallback doorReportOperationCompleted;
        
        private System.Threading.SendOrPostCallback accessLabReportOperationCompleted;
        
        private System.Threading.SendOrPostCallback employeeDoorsReportOperationCompleted;
        
        private System.Threading.SendOrPostCallback heartBeatOperationCompleted;
        
        private System.Threading.SendOrPostCallback searchDoorsCardsOperationCompleted;
        
        private bool useDefaultCredentialsSetExplicitly;
        
        /// <remarks/>
        public wsdlService() {
            this.Url = global::CtrEmailService.Properties.Settings.Default.CtrEmailService_WebReference_wsdlService;
            if ((this.IsLocalFileSystemWebService(this.Url) == true)) {
                this.UseDefaultCredentials = true;
                this.useDefaultCredentialsSetExplicitly = false;
            }
            else {
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        public new string Url {
            get {
                return base.Url;
            }
            set {
                if ((((this.IsLocalFileSystemWebService(base.Url) == true) 
                            && (this.useDefaultCredentialsSetExplicitly == false)) 
                            && (this.IsLocalFileSystemWebService(value) == false))) {
                    base.UseDefaultCredentials = false;
                }
                base.Url = value;
            }
        }
        
        public new bool UseDefaultCredentials {
            get {
                return base.UseDefaultCredentials;
            }
            set {
                base.UseDefaultCredentials = value;
                this.useDefaultCredentialsSetExplicitly = true;
            }
        }
        
        /// <remarks/>
        public event loginCompletedEventHandler loginCompleted;
        
        /// <remarks/>
        public event getCtrsCompletedEventHandler getCtrsCompleted;
        
        /// <remarks/>
        public event getYaoCtrsCompletedEventHandler getYaoCtrsCompleted;
        
        /// <remarks/>
        public event getStatusCompletedEventHandler getStatusCompleted;
        
        /// <remarks/>
        public event getCtrCardsCompletedEventHandler getCtrCardsCompleted;
        
        /// <remarks/>
        public event getDbCardsCompletedEventHandler getDbCardsCompleted;
        
        /// <remarks/>
        public event setDbCardsCompletedEventHandler setDbCardsCompleted;
        
        /// <remarks/>
        public event getDoorsCompletedEventHandler getDoorsCompleted;
        
        /// <remarks/>
        public event insertReserveCardsCompletedEventHandler insertReserveCardsCompleted;
        
        /// <remarks/>
        public event searchReserveCardsCompletedEventHandler searchReserveCardsCompleted;
        
        /// <remarks/>
        public event deleteReserveCardsCompletedEventHandler deleteReserveCardsCompleted;
        
        /// <remarks/>
        public event insertPublicDoorsCompletedEventHandler insertPublicDoorsCompleted;
        
        /// <remarks/>
        public event updatePublicDoorsCompletedEventHandler updatePublicDoorsCompleted;
        
        /// <remarks/>
        public event searchPublicDoorsCompletedEventHandler searchPublicDoorsCompleted;
        
        /// <remarks/>
        public event deletePublicDoorsCompletedEventHandler deletePublicDoorsCompleted;
        
        /// <remarks/>
        public event updateDoorsCompletedEventHandler updateDoorsCompleted;
        
        /// <remarks/>
        public event insertEmployeesCompletedEventHandler insertEmployeesCompleted;
        
        /// <remarks/>
        public event deleteEmployeesCompletedEventHandler deleteEmployeesCompleted;
        
        /// <remarks/>
        public event updateEmployeesCompletedEventHandler updateEmployeesCompleted;
        
        /// <remarks/>
        public event searchEmployeesCompletedEventHandler searchEmployeesCompleted;
        
        /// <remarks/>
        public event insertCardsCompletedEventHandler insertCardsCompleted;
        
        /// <remarks/>
        public event updateCardsCompletedEventHandler updateCardsCompleted;
        
        /// <remarks/>
        public event deleteCardsCompletedEventHandler deleteCardsCompleted;
        
        /// <remarks/>
        public event searchCardsCompletedEventHandler searchCardsCompleted;
        
        /// <remarks/>
        public event insertDeptsCompletedEventHandler insertDeptsCompleted;
        
        /// <remarks/>
        public event updateDeptsCompletedEventHandler updateDeptsCompleted;
        
        /// <remarks/>
        public event deleteDeptsCompletedEventHandler deleteDeptsCompleted;
        
        /// <remarks/>
        public event searchDeptsCompletedEventHandler searchDeptsCompleted;
        
        /// <remarks/>
        public event getCardsDoorsCompletedEventHandler getCardsDoorsCompleted;
        
        /// <remarks/>
        public event searchCardsDoorsCompletedEventHandler searchCardsDoorsCompleted;
        
        /// <remarks/>
        public event asyncSetCardsDoorsCompletedEventHandler asyncSetCardsDoorsCompleted;
        
        /// <remarks/>
        public event asyncSetCardsDoorsXCompletedEventHandler asyncSetCardsDoorsXCompleted;
        
        /// <remarks/>
        public event setCardsDoorsCompletedEventHandler setCardsDoorsCompleted;
        
        /// <remarks/>
        public event setCardsDoorsSyncCompletedEventHandler setCardsDoorsSyncCompleted;
        
        /// <remarks/>
        public event normalCardeventReportCompletedEventHandler normalCardeventReportCompleted;
        
        /// <remarks/>
        public event abnormalCardeventReportCompletedEventHandler abnormalCardeventReportCompleted;
        
        /// <remarks/>
        public event doorRelationReportCompletedEventHandler doorRelationReportCompleted;
        
        /// <remarks/>
        public event abnormalEmployeeeventReportCompletedEventHandler abnormalEmployeeeventReportCompleted;
        
        /// <remarks/>
        public event employeeAuthReportCompletedEventHandler employeeAuthReportCompleted;
        
        /// <remarks/>
        public event doorReportCompletedEventHandler doorReportCompleted;
        
        /// <remarks/>
        public event accessLabReportCompletedEventHandler accessLabReportCompleted;
        
        /// <remarks/>
        public event employeeDoorsReportCompletedEventHandler employeeDoorsReportCompleted;
        
        /// <remarks/>
        public event heartBeatCompletedEventHandler heartBeatCompleted;
        
        /// <remarks/>
        public event searchDoorsCardsCompletedEventHandler searchDoorsCardsCompleted;
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("loginReturn")]
        public string login(string username, string password) {
            object[] results = this.Invoke("login", new object[] {
                        username,
                        password});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void loginAsync(string username, string password) {
            this.loginAsync(username, password, null);
        }
        
        /// <remarks/>
        public void loginAsync(string username, string password, object userState) {
            if ((this.loginOperationCompleted == null)) {
                this.loginOperationCompleted = new System.Threading.SendOrPostCallback(this.OnloginOperationCompleted);
            }
            this.InvokeAsync("login", new object[] {
                        username,
                        password}, this.loginOperationCompleted, userState);
        }
        
        private void OnloginOperationCompleted(object arg) {
            if ((this.loginCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.loginCompleted(this, new loginCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("getCtrsReturn")]
        public string getCtrs(string sessionid) {
            object[] results = this.Invoke("getCtrs", new object[] {
                        sessionid});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void getCtrsAsync(string sessionid) {
            this.getCtrsAsync(sessionid, null);
        }
        
        /// <remarks/>
        public void getCtrsAsync(string sessionid, object userState) {
            if ((this.getCtrsOperationCompleted == null)) {
                this.getCtrsOperationCompleted = new System.Threading.SendOrPostCallback(this.OngetCtrsOperationCompleted);
            }
            this.InvokeAsync("getCtrs", new object[] {
                        sessionid}, this.getCtrsOperationCompleted, userState);
        }
        
        private void OngetCtrsOperationCompleted(object arg) {
            if ((this.getCtrsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.getCtrsCompleted(this, new getCtrsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("getYaoCtrsReturn")]
        public string getYaoCtrs(string sessionid) {
            object[] results = this.Invoke("getYaoCtrs", new object[] {
                        sessionid});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void getYaoCtrsAsync(string sessionid) {
            this.getYaoCtrsAsync(sessionid, null);
        }
        
        /// <remarks/>
        public void getYaoCtrsAsync(string sessionid, object userState) {
            if ((this.getYaoCtrsOperationCompleted == null)) {
                this.getYaoCtrsOperationCompleted = new System.Threading.SendOrPostCallback(this.OngetYaoCtrsOperationCompleted);
            }
            this.InvokeAsync("getYaoCtrs", new object[] {
                        sessionid}, this.getYaoCtrsOperationCompleted, userState);
        }
        
        private void OngetYaoCtrsOperationCompleted(object arg) {
            if ((this.getYaoCtrsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.getYaoCtrsCompleted(this, new getYaoCtrsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("getStatusReturn")]
        public string getStatus(string sessionid) {
            object[] results = this.Invoke("getStatus", new object[] {
                        sessionid});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void getStatusAsync(string sessionid) {
            this.getStatusAsync(sessionid, null);
        }
        
        /// <remarks/>
        public void getStatusAsync(string sessionid, object userState) {
            if ((this.getStatusOperationCompleted == null)) {
                this.getStatusOperationCompleted = new System.Threading.SendOrPostCallback(this.OngetStatusOperationCompleted);
            }
            this.InvokeAsync("getStatus", new object[] {
                        sessionid}, this.getStatusOperationCompleted, userState);
        }
        
        private void OngetStatusOperationCompleted(object arg) {
            if ((this.getStatusCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.getStatusCompleted(this, new getStatusCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("getCtrCardsReturn")]
        public string getCtrCards(string sessionid, string argstr) {
            object[] results = this.Invoke("getCtrCards", new object[] {
                        sessionid,
                        argstr});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void getCtrCardsAsync(string sessionid, string argstr) {
            this.getCtrCardsAsync(sessionid, argstr, null);
        }
        
        /// <remarks/>
        public void getCtrCardsAsync(string sessionid, string argstr, object userState) {
            if ((this.getCtrCardsOperationCompleted == null)) {
                this.getCtrCardsOperationCompleted = new System.Threading.SendOrPostCallback(this.OngetCtrCardsOperationCompleted);
            }
            this.InvokeAsync("getCtrCards", new object[] {
                        sessionid,
                        argstr}, this.getCtrCardsOperationCompleted, userState);
        }
        
        private void OngetCtrCardsOperationCompleted(object arg) {
            if ((this.getCtrCardsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.getCtrCardsCompleted(this, new getCtrCardsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("getDbCardsReturn")]
        public string getDbCards(string sessionid, int ctraddress) {
            object[] results = this.Invoke("getDbCards", new object[] {
                        sessionid,
                        ctraddress});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void getDbCardsAsync(string sessionid, int ctraddress) {
            this.getDbCardsAsync(sessionid, ctraddress, null);
        }
        
        /// <remarks/>
        public void getDbCardsAsync(string sessionid, int ctraddress, object userState) {
            if ((this.getDbCardsOperationCompleted == null)) {
                this.getDbCardsOperationCompleted = new System.Threading.SendOrPostCallback(this.OngetDbCardsOperationCompleted);
            }
            this.InvokeAsync("getDbCards", new object[] {
                        sessionid,
                        ctraddress}, this.getDbCardsOperationCompleted, userState);
        }
        
        private void OngetDbCardsOperationCompleted(object arg) {
            if ((this.getDbCardsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.getDbCardsCompleted(this, new getDbCardsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("setDbCardsReturn")]
        public string setDbCards(string sessionid, string cardsStr) {
            object[] results = this.Invoke("setDbCards", new object[] {
                        sessionid,
                        cardsStr});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void setDbCardsAsync(string sessionid, string cardsStr) {
            this.setDbCardsAsync(sessionid, cardsStr, null);
        }
        
        /// <remarks/>
        public void setDbCardsAsync(string sessionid, string cardsStr, object userState) {
            if ((this.setDbCardsOperationCompleted == null)) {
                this.setDbCardsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnsetDbCardsOperationCompleted);
            }
            this.InvokeAsync("setDbCards", new object[] {
                        sessionid,
                        cardsStr}, this.setDbCardsOperationCompleted, userState);
        }
        
        private void OnsetDbCardsOperationCompleted(object arg) {
            if ((this.setDbCardsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.setDbCardsCompleted(this, new setDbCardsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("getDoorsReturn")]
        public string getDoors(string sessionid, int ctrid) {
            object[] results = this.Invoke("getDoors", new object[] {
                        sessionid,
                        ctrid});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void getDoorsAsync(string sessionid, int ctrid) {
            this.getDoorsAsync(sessionid, ctrid, null);
        }
        
        /// <remarks/>
        public void getDoorsAsync(string sessionid, int ctrid, object userState) {
            if ((this.getDoorsOperationCompleted == null)) {
                this.getDoorsOperationCompleted = new System.Threading.SendOrPostCallback(this.OngetDoorsOperationCompleted);
            }
            this.InvokeAsync("getDoors", new object[] {
                        sessionid,
                        ctrid}, this.getDoorsOperationCompleted, userState);
        }
        
        private void OngetDoorsOperationCompleted(object arg) {
            if ((this.getDoorsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.getDoorsCompleted(this, new getDoorsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("insertReserveCardsReturn")]
        public string insertReserveCards(string sessionid, string cards, string ip, string userid) {
            object[] results = this.Invoke("insertReserveCards", new object[] {
                        sessionid,
                        cards,
                        ip,
                        userid});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void insertReserveCardsAsync(string sessionid, string cards, string ip, string userid) {
            this.insertReserveCardsAsync(sessionid, cards, ip, userid, null);
        }
        
        /// <remarks/>
        public void insertReserveCardsAsync(string sessionid, string cards, string ip, string userid, object userState) {
            if ((this.insertReserveCardsOperationCompleted == null)) {
                this.insertReserveCardsOperationCompleted = new System.Threading.SendOrPostCallback(this.OninsertReserveCardsOperationCompleted);
            }
            this.InvokeAsync("insertReserveCards", new object[] {
                        sessionid,
                        cards,
                        ip,
                        userid}, this.insertReserveCardsOperationCompleted, userState);
        }
        
        private void OninsertReserveCardsOperationCompleted(object arg) {
            if ((this.insertReserveCardsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.insertReserveCardsCompleted(this, new insertReserveCardsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("searchReserveCardsReturn")]
        public string searchReserveCards(string sessionid, string cards) {
            object[] results = this.Invoke("searchReserveCards", new object[] {
                        sessionid,
                        cards});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void searchReserveCardsAsync(string sessionid, string cards) {
            this.searchReserveCardsAsync(sessionid, cards, null);
        }
        
        /// <remarks/>
        public void searchReserveCardsAsync(string sessionid, string cards, object userState) {
            if ((this.searchReserveCardsOperationCompleted == null)) {
                this.searchReserveCardsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnsearchReserveCardsOperationCompleted);
            }
            this.InvokeAsync("searchReserveCards", new object[] {
                        sessionid,
                        cards}, this.searchReserveCardsOperationCompleted, userState);
        }
        
        private void OnsearchReserveCardsOperationCompleted(object arg) {
            if ((this.searchReserveCardsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.searchReserveCardsCompleted(this, new searchReserveCardsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("deleteReserveCardsReturn")]
        public string deleteReserveCards(string sessionid, string cards) {
            object[] results = this.Invoke("deleteReserveCards", new object[] {
                        sessionid,
                        cards});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void deleteReserveCardsAsync(string sessionid, string cards) {
            this.deleteReserveCardsAsync(sessionid, cards, null);
        }
        
        /// <remarks/>
        public void deleteReserveCardsAsync(string sessionid, string cards, object userState) {
            if ((this.deleteReserveCardsOperationCompleted == null)) {
                this.deleteReserveCardsOperationCompleted = new System.Threading.SendOrPostCallback(this.OndeleteReserveCardsOperationCompleted);
            }
            this.InvokeAsync("deleteReserveCards", new object[] {
                        sessionid,
                        cards}, this.deleteReserveCardsOperationCompleted, userState);
        }
        
        private void OndeleteReserveCardsOperationCompleted(object arg) {
            if ((this.deleteReserveCardsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.deleteReserveCardsCompleted(this, new deleteReserveCardsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("insertPublicDoorsReturn")]
        public string insertPublicDoors(string sessionid, string doorInfo, string ip, string userid) {
            object[] results = this.Invoke("insertPublicDoors", new object[] {
                        sessionid,
                        doorInfo,
                        ip,
                        userid});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void insertPublicDoorsAsync(string sessionid, string doorInfo, string ip, string userid) {
            this.insertPublicDoorsAsync(sessionid, doorInfo, ip, userid, null);
        }
        
        /// <remarks/>
        public void insertPublicDoorsAsync(string sessionid, string doorInfo, string ip, string userid, object userState) {
            if ((this.insertPublicDoorsOperationCompleted == null)) {
                this.insertPublicDoorsOperationCompleted = new System.Threading.SendOrPostCallback(this.OninsertPublicDoorsOperationCompleted);
            }
            this.InvokeAsync("insertPublicDoors", new object[] {
                        sessionid,
                        doorInfo,
                        ip,
                        userid}, this.insertPublicDoorsOperationCompleted, userState);
        }
        
        private void OninsertPublicDoorsOperationCompleted(object arg) {
            if ((this.insertPublicDoorsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.insertPublicDoorsCompleted(this, new insertPublicDoorsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("updatePublicDoorsReturn")]
        public string updatePublicDoors(string sessionid, string doorInfo, string ip, string userid) {
            object[] results = this.Invoke("updatePublicDoors", new object[] {
                        sessionid,
                        doorInfo,
                        ip,
                        userid});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void updatePublicDoorsAsync(string sessionid, string doorInfo, string ip, string userid) {
            this.updatePublicDoorsAsync(sessionid, doorInfo, ip, userid, null);
        }
        
        /// <remarks/>
        public void updatePublicDoorsAsync(string sessionid, string doorInfo, string ip, string userid, object userState) {
            if ((this.updatePublicDoorsOperationCompleted == null)) {
                this.updatePublicDoorsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnupdatePublicDoorsOperationCompleted);
            }
            this.InvokeAsync("updatePublicDoors", new object[] {
                        sessionid,
                        doorInfo,
                        ip,
                        userid}, this.updatePublicDoorsOperationCompleted, userState);
        }
        
        private void OnupdatePublicDoorsOperationCompleted(object arg) {
            if ((this.updatePublicDoorsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.updatePublicDoorsCompleted(this, new updatePublicDoorsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("searchPublicDoorsReturn")]
        public string searchPublicDoors(string sessionid, string doorInfo) {
            object[] results = this.Invoke("searchPublicDoors", new object[] {
                        sessionid,
                        doorInfo});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void searchPublicDoorsAsync(string sessionid, string doorInfo) {
            this.searchPublicDoorsAsync(sessionid, doorInfo, null);
        }
        
        /// <remarks/>
        public void searchPublicDoorsAsync(string sessionid, string doorInfo, object userState) {
            if ((this.searchPublicDoorsOperationCompleted == null)) {
                this.searchPublicDoorsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnsearchPublicDoorsOperationCompleted);
            }
            this.InvokeAsync("searchPublicDoors", new object[] {
                        sessionid,
                        doorInfo}, this.searchPublicDoorsOperationCompleted, userState);
        }
        
        private void OnsearchPublicDoorsOperationCompleted(object arg) {
            if ((this.searchPublicDoorsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.searchPublicDoorsCompleted(this, new searchPublicDoorsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("deletePublicDoorsReturn")]
        public string deletePublicDoors(string sessionid, string doorInfo, string ip, string userid) {
            object[] results = this.Invoke("deletePublicDoors", new object[] {
                        sessionid,
                        doorInfo,
                        ip,
                        userid});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void deletePublicDoorsAsync(string sessionid, string doorInfo, string ip, string userid) {
            this.deletePublicDoorsAsync(sessionid, doorInfo, ip, userid, null);
        }
        
        /// <remarks/>
        public void deletePublicDoorsAsync(string sessionid, string doorInfo, string ip, string userid, object userState) {
            if ((this.deletePublicDoorsOperationCompleted == null)) {
                this.deletePublicDoorsOperationCompleted = new System.Threading.SendOrPostCallback(this.OndeletePublicDoorsOperationCompleted);
            }
            this.InvokeAsync("deletePublicDoors", new object[] {
                        sessionid,
                        doorInfo,
                        ip,
                        userid}, this.deletePublicDoorsOperationCompleted, userState);
        }
        
        private void OndeletePublicDoorsOperationCompleted(object arg) {
            if ((this.deletePublicDoorsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.deletePublicDoorsCompleted(this, new deletePublicDoorsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("updateDoorsReturn")]
        public string updateDoors(string sessionid, string doorInfo, string ip, string userid) {
            object[] results = this.Invoke("updateDoors", new object[] {
                        sessionid,
                        doorInfo,
                        ip,
                        userid});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void updateDoorsAsync(string sessionid, string doorInfo, string ip, string userid) {
            this.updateDoorsAsync(sessionid, doorInfo, ip, userid, null);
        }
        
        /// <remarks/>
        public void updateDoorsAsync(string sessionid, string doorInfo, string ip, string userid, object userState) {
            if ((this.updateDoorsOperationCompleted == null)) {
                this.updateDoorsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnupdateDoorsOperationCompleted);
            }
            this.InvokeAsync("updateDoors", new object[] {
                        sessionid,
                        doorInfo,
                        ip,
                        userid}, this.updateDoorsOperationCompleted, userState);
        }
        
        private void OnupdateDoorsOperationCompleted(object arg) {
            if ((this.updateDoorsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.updateDoorsCompleted(this, new updateDoorsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("insertEmployeesReturn")]
        public string insertEmployees(string sessionid, string employees) {
            object[] results = this.Invoke("insertEmployees", new object[] {
                        sessionid,
                        employees});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void insertEmployeesAsync(string sessionid, string employees) {
            this.insertEmployeesAsync(sessionid, employees, null);
        }
        
        /// <remarks/>
        public void insertEmployeesAsync(string sessionid, string employees, object userState) {
            if ((this.insertEmployeesOperationCompleted == null)) {
                this.insertEmployeesOperationCompleted = new System.Threading.SendOrPostCallback(this.OninsertEmployeesOperationCompleted);
            }
            this.InvokeAsync("insertEmployees", new object[] {
                        sessionid,
                        employees}, this.insertEmployeesOperationCompleted, userState);
        }
        
        private void OninsertEmployeesOperationCompleted(object arg) {
            if ((this.insertEmployeesCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.insertEmployeesCompleted(this, new insertEmployeesCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("deleteEmployeesReturn")]
        public string deleteEmployees(string sessionid, string employees) {
            object[] results = this.Invoke("deleteEmployees", new object[] {
                        sessionid,
                        employees});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void deleteEmployeesAsync(string sessionid, string employees) {
            this.deleteEmployeesAsync(sessionid, employees, null);
        }
        
        /// <remarks/>
        public void deleteEmployeesAsync(string sessionid, string employees, object userState) {
            if ((this.deleteEmployeesOperationCompleted == null)) {
                this.deleteEmployeesOperationCompleted = new System.Threading.SendOrPostCallback(this.OndeleteEmployeesOperationCompleted);
            }
            this.InvokeAsync("deleteEmployees", new object[] {
                        sessionid,
                        employees}, this.deleteEmployeesOperationCompleted, userState);
        }
        
        private void OndeleteEmployeesOperationCompleted(object arg) {
            if ((this.deleteEmployeesCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.deleteEmployeesCompleted(this, new deleteEmployeesCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("updateEmployeesReturn")]
        public string updateEmployees(string sessionid, string employees) {
            object[] results = this.Invoke("updateEmployees", new object[] {
                        sessionid,
                        employees});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void updateEmployeesAsync(string sessionid, string employees) {
            this.updateEmployeesAsync(sessionid, employees, null);
        }
        
        /// <remarks/>
        public void updateEmployeesAsync(string sessionid, string employees, object userState) {
            if ((this.updateEmployeesOperationCompleted == null)) {
                this.updateEmployeesOperationCompleted = new System.Threading.SendOrPostCallback(this.OnupdateEmployeesOperationCompleted);
            }
            this.InvokeAsync("updateEmployees", new object[] {
                        sessionid,
                        employees}, this.updateEmployeesOperationCompleted, userState);
        }
        
        private void OnupdateEmployeesOperationCompleted(object arg) {
            if ((this.updateEmployeesCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.updateEmployeesCompleted(this, new updateEmployeesCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("searchEmployeesReturn")]
        public string searchEmployees(string sessionid, string cards) {
            object[] results = this.Invoke("searchEmployees", new object[] {
                        sessionid,
                        cards});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void searchEmployeesAsync(string sessionid, string cards) {
            this.searchEmployeesAsync(sessionid, cards, null);
        }
        
        /// <remarks/>
        public void searchEmployeesAsync(string sessionid, string cards, object userState) {
            if ((this.searchEmployeesOperationCompleted == null)) {
                this.searchEmployeesOperationCompleted = new System.Threading.SendOrPostCallback(this.OnsearchEmployeesOperationCompleted);
            }
            this.InvokeAsync("searchEmployees", new object[] {
                        sessionid,
                        cards}, this.searchEmployeesOperationCompleted, userState);
        }
        
        private void OnsearchEmployeesOperationCompleted(object arg) {
            if ((this.searchEmployeesCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.searchEmployeesCompleted(this, new searchEmployeesCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("insertCardsReturn")]
        public string insertCards(string sessionid, string cards) {
            object[] results = this.Invoke("insertCards", new object[] {
                        sessionid,
                        cards});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void insertCardsAsync(string sessionid, string cards) {
            this.insertCardsAsync(sessionid, cards, null);
        }
        
        /// <remarks/>
        public void insertCardsAsync(string sessionid, string cards, object userState) {
            if ((this.insertCardsOperationCompleted == null)) {
                this.insertCardsOperationCompleted = new System.Threading.SendOrPostCallback(this.OninsertCardsOperationCompleted);
            }
            this.InvokeAsync("insertCards", new object[] {
                        sessionid,
                        cards}, this.insertCardsOperationCompleted, userState);
        }
        
        private void OninsertCardsOperationCompleted(object arg) {
            if ((this.insertCardsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.insertCardsCompleted(this, new insertCardsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("updateCardsReturn")]
        public string updateCards(string sessionid, string cards) {
            object[] results = this.Invoke("updateCards", new object[] {
                        sessionid,
                        cards});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void updateCardsAsync(string sessionid, string cards) {
            this.updateCardsAsync(sessionid, cards, null);
        }
        
        /// <remarks/>
        public void updateCardsAsync(string sessionid, string cards, object userState) {
            if ((this.updateCardsOperationCompleted == null)) {
                this.updateCardsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnupdateCardsOperationCompleted);
            }
            this.InvokeAsync("updateCards", new object[] {
                        sessionid,
                        cards}, this.updateCardsOperationCompleted, userState);
        }
        
        private void OnupdateCardsOperationCompleted(object arg) {
            if ((this.updateCardsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.updateCardsCompleted(this, new updateCardsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("deleteCardsReturn")]
        public string deleteCards(string sessionid, string cards) {
            object[] results = this.Invoke("deleteCards", new object[] {
                        sessionid,
                        cards});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void deleteCardsAsync(string sessionid, string cards) {
            this.deleteCardsAsync(sessionid, cards, null);
        }
        
        /// <remarks/>
        public void deleteCardsAsync(string sessionid, string cards, object userState) {
            if ((this.deleteCardsOperationCompleted == null)) {
                this.deleteCardsOperationCompleted = new System.Threading.SendOrPostCallback(this.OndeleteCardsOperationCompleted);
            }
            this.InvokeAsync("deleteCards", new object[] {
                        sessionid,
                        cards}, this.deleteCardsOperationCompleted, userState);
        }
        
        private void OndeleteCardsOperationCompleted(object arg) {
            if ((this.deleteCardsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.deleteCardsCompleted(this, new deleteCardsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("searchCardsReturn")]
        public string searchCards(string sessionid, string cards) {
            object[] results = this.Invoke("searchCards", new object[] {
                        sessionid,
                        cards});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void searchCardsAsync(string sessionid, string cards) {
            this.searchCardsAsync(sessionid, cards, null);
        }
        
        /// <remarks/>
        public void searchCardsAsync(string sessionid, string cards, object userState) {
            if ((this.searchCardsOperationCompleted == null)) {
                this.searchCardsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnsearchCardsOperationCompleted);
            }
            this.InvokeAsync("searchCards", new object[] {
                        sessionid,
                        cards}, this.searchCardsOperationCompleted, userState);
        }
        
        private void OnsearchCardsOperationCompleted(object arg) {
            if ((this.searchCardsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.searchCardsCompleted(this, new searchCardsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("insertDeptsReturn")]
        public string insertDepts(string sessionid, string depts) {
            object[] results = this.Invoke("insertDepts", new object[] {
                        sessionid,
                        depts});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void insertDeptsAsync(string sessionid, string depts) {
            this.insertDeptsAsync(sessionid, depts, null);
        }
        
        /// <remarks/>
        public void insertDeptsAsync(string sessionid, string depts, object userState) {
            if ((this.insertDeptsOperationCompleted == null)) {
                this.insertDeptsOperationCompleted = new System.Threading.SendOrPostCallback(this.OninsertDeptsOperationCompleted);
            }
            this.InvokeAsync("insertDepts", new object[] {
                        sessionid,
                        depts}, this.insertDeptsOperationCompleted, userState);
        }
        
        private void OninsertDeptsOperationCompleted(object arg) {
            if ((this.insertDeptsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.insertDeptsCompleted(this, new insertDeptsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("updateDeptsReturn")]
        public string updateDepts(string sessionid, string depts) {
            object[] results = this.Invoke("updateDepts", new object[] {
                        sessionid,
                        depts});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void updateDeptsAsync(string sessionid, string depts) {
            this.updateDeptsAsync(sessionid, depts, null);
        }
        
        /// <remarks/>
        public void updateDeptsAsync(string sessionid, string depts, object userState) {
            if ((this.updateDeptsOperationCompleted == null)) {
                this.updateDeptsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnupdateDeptsOperationCompleted);
            }
            this.InvokeAsync("updateDepts", new object[] {
                        sessionid,
                        depts}, this.updateDeptsOperationCompleted, userState);
        }
        
        private void OnupdateDeptsOperationCompleted(object arg) {
            if ((this.updateDeptsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.updateDeptsCompleted(this, new updateDeptsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("deleteDeptsReturn")]
        public string deleteDepts(string sessionid, string depts) {
            object[] results = this.Invoke("deleteDepts", new object[] {
                        sessionid,
                        depts});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void deleteDeptsAsync(string sessionid, string depts) {
            this.deleteDeptsAsync(sessionid, depts, null);
        }
        
        /// <remarks/>
        public void deleteDeptsAsync(string sessionid, string depts, object userState) {
            if ((this.deleteDeptsOperationCompleted == null)) {
                this.deleteDeptsOperationCompleted = new System.Threading.SendOrPostCallback(this.OndeleteDeptsOperationCompleted);
            }
            this.InvokeAsync("deleteDepts", new object[] {
                        sessionid,
                        depts}, this.deleteDeptsOperationCompleted, userState);
        }
        
        private void OndeleteDeptsOperationCompleted(object arg) {
            if ((this.deleteDeptsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.deleteDeptsCompleted(this, new deleteDeptsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("searchDeptsReturn")]
        public string searchDepts(string sessionid, string depts) {
            object[] results = this.Invoke("searchDepts", new object[] {
                        sessionid,
                        depts});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void searchDeptsAsync(string sessionid, string depts) {
            this.searchDeptsAsync(sessionid, depts, null);
        }
        
        /// <remarks/>
        public void searchDeptsAsync(string sessionid, string depts, object userState) {
            if ((this.searchDeptsOperationCompleted == null)) {
                this.searchDeptsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnsearchDeptsOperationCompleted);
            }
            this.InvokeAsync("searchDepts", new object[] {
                        sessionid,
                        depts}, this.searchDeptsOperationCompleted, userState);
        }
        
        private void OnsearchDeptsOperationCompleted(object arg) {
            if ((this.searchDeptsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.searchDeptsCompleted(this, new searchDeptsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("getCardsDoorsReturn")]
        public string getCardsDoors(string sessionid, string card) {
            object[] results = this.Invoke("getCardsDoors", new object[] {
                        sessionid,
                        card});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void getCardsDoorsAsync(string sessionid, string card) {
            this.getCardsDoorsAsync(sessionid, card, null);
        }
        
        /// <remarks/>
        public void getCardsDoorsAsync(string sessionid, string card, object userState) {
            if ((this.getCardsDoorsOperationCompleted == null)) {
                this.getCardsDoorsOperationCompleted = new System.Threading.SendOrPostCallback(this.OngetCardsDoorsOperationCompleted);
            }
            this.InvokeAsync("getCardsDoors", new object[] {
                        sessionid,
                        card}, this.getCardsDoorsOperationCompleted, userState);
        }
        
        private void OngetCardsDoorsOperationCompleted(object arg) {
            if ((this.getCardsDoorsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.getCardsDoorsCompleted(this, new getCardsDoorsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("searchCardsDoorsReturn")]
        public string searchCardsDoors(string sessionid, string depts, string leaders, string posts) {
            object[] results = this.Invoke("searchCardsDoors", new object[] {
                        sessionid,
                        depts,
                        leaders,
                        posts});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void searchCardsDoorsAsync(string sessionid, string depts, string leaders, string posts) {
            this.searchCardsDoorsAsync(sessionid, depts, leaders, posts, null);
        }
        
        /// <remarks/>
        public void searchCardsDoorsAsync(string sessionid, string depts, string leaders, string posts, object userState) {
            if ((this.searchCardsDoorsOperationCompleted == null)) {
                this.searchCardsDoorsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnsearchCardsDoorsOperationCompleted);
            }
            this.InvokeAsync("searchCardsDoors", new object[] {
                        sessionid,
                        depts,
                        leaders,
                        posts}, this.searchCardsDoorsOperationCompleted, userState);
        }
        
        private void OnsearchCardsDoorsOperationCompleted(object arg) {
            if ((this.searchCardsDoorsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.searchCardsDoorsCompleted(this, new searchCardsDoorsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("asyncSetCardsDoorsReturn")]
        public string asyncSetCardsDoors(string sessionid, string cards, int commandno) {
            object[] results = this.Invoke("asyncSetCardsDoors", new object[] {
                        sessionid,
                        cards,
                        commandno});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void asyncSetCardsDoorsAsync(string sessionid, string cards, int commandno) {
            this.asyncSetCardsDoorsAsync(sessionid, cards, commandno, null);
        }
        
        /// <remarks/>
        public void asyncSetCardsDoorsAsync(string sessionid, string cards, int commandno, object userState) {
            if ((this.asyncSetCardsDoorsOperationCompleted == null)) {
                this.asyncSetCardsDoorsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnasyncSetCardsDoorsOperationCompleted);
            }
            this.InvokeAsync("asyncSetCardsDoors", new object[] {
                        sessionid,
                        cards,
                        commandno}, this.asyncSetCardsDoorsOperationCompleted, userState);
        }
        
        private void OnasyncSetCardsDoorsOperationCompleted(object arg) {
            if ((this.asyncSetCardsDoorsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.asyncSetCardsDoorsCompleted(this, new asyncSetCardsDoorsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("asyncSetCardsDoorsXReturn")]
        public string asyncSetCardsDoorsX(string sessionid, string cards, int commandno) {
            object[] results = this.Invoke("asyncSetCardsDoorsX", new object[] {
                        sessionid,
                        cards,
                        commandno});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void asyncSetCardsDoorsXAsync(string sessionid, string cards, int commandno) {
            this.asyncSetCardsDoorsXAsync(sessionid, cards, commandno, null);
        }
        
        /// <remarks/>
        public void asyncSetCardsDoorsXAsync(string sessionid, string cards, int commandno, object userState) {
            if ((this.asyncSetCardsDoorsXOperationCompleted == null)) {
                this.asyncSetCardsDoorsXOperationCompleted = new System.Threading.SendOrPostCallback(this.OnasyncSetCardsDoorsXOperationCompleted);
            }
            this.InvokeAsync("asyncSetCardsDoorsX", new object[] {
                        sessionid,
                        cards,
                        commandno}, this.asyncSetCardsDoorsXOperationCompleted, userState);
        }
        
        private void OnasyncSetCardsDoorsXOperationCompleted(object arg) {
            if ((this.asyncSetCardsDoorsXCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.asyncSetCardsDoorsXCompleted(this, new asyncSetCardsDoorsXCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("setCardsDoorsReturn")]
        public string setCardsDoors(string sessionid, string cards) {
            object[] results = this.Invoke("setCardsDoors", new object[] {
                        sessionid,
                        cards});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void setCardsDoorsAsync(string sessionid, string cards) {
            this.setCardsDoorsAsync(sessionid, cards, null);
        }
        
        /// <remarks/>
        public void setCardsDoorsAsync(string sessionid, string cards, object userState) {
            if ((this.setCardsDoorsOperationCompleted == null)) {
                this.setCardsDoorsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnsetCardsDoorsOperationCompleted);
            }
            this.InvokeAsync("setCardsDoors", new object[] {
                        sessionid,
                        cards}, this.setCardsDoorsOperationCompleted, userState);
        }
        
        private void OnsetCardsDoorsOperationCompleted(object arg) {
            if ((this.setCardsDoorsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.setCardsDoorsCompleted(this, new setCardsDoorsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("setCardsDoorsSyncReturn")]
        public string setCardsDoorsSync(string sessionid, string cards) {
            object[] results = this.Invoke("setCardsDoorsSync", new object[] {
                        sessionid,
                        cards});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void setCardsDoorsSyncAsync(string sessionid, string cards) {
            this.setCardsDoorsSyncAsync(sessionid, cards, null);
        }
        
        /// <remarks/>
        public void setCardsDoorsSyncAsync(string sessionid, string cards, object userState) {
            if ((this.setCardsDoorsSyncOperationCompleted == null)) {
                this.setCardsDoorsSyncOperationCompleted = new System.Threading.SendOrPostCallback(this.OnsetCardsDoorsSyncOperationCompleted);
            }
            this.InvokeAsync("setCardsDoorsSync", new object[] {
                        sessionid,
                        cards}, this.setCardsDoorsSyncOperationCompleted, userState);
        }
        
        private void OnsetCardsDoorsSyncOperationCompleted(object arg) {
            if ((this.setCardsDoorsSyncCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.setCardsDoorsSyncCompleted(this, new setCardsDoorsSyncCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("normalCardeventReportReturn")]
        public string normalCardeventReport(string sessionid, string condition, string filetype) {
            object[] results = this.Invoke("normalCardeventReport", new object[] {
                        sessionid,
                        condition,
                        filetype});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void normalCardeventReportAsync(string sessionid, string condition, string filetype) {
            this.normalCardeventReportAsync(sessionid, condition, filetype, null);
        }
        
        /// <remarks/>
        public void normalCardeventReportAsync(string sessionid, string condition, string filetype, object userState) {
            if ((this.normalCardeventReportOperationCompleted == null)) {
                this.normalCardeventReportOperationCompleted = new System.Threading.SendOrPostCallback(this.OnnormalCardeventReportOperationCompleted);
            }
            this.InvokeAsync("normalCardeventReport", new object[] {
                        sessionid,
                        condition,
                        filetype}, this.normalCardeventReportOperationCompleted, userState);
        }
        
        private void OnnormalCardeventReportOperationCompleted(object arg) {
            if ((this.normalCardeventReportCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.normalCardeventReportCompleted(this, new normalCardeventReportCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("abnormalCardeventReportReturn")]
        public string abnormalCardeventReport(string sessionid, string condition, int filetype) {
            object[] results = this.Invoke("abnormalCardeventReport", new object[] {
                        sessionid,
                        condition,
                        filetype});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void abnormalCardeventReportAsync(string sessionid, string condition, int filetype) {
            this.abnormalCardeventReportAsync(sessionid, condition, filetype, null);
        }
        
        /// <remarks/>
        public void abnormalCardeventReportAsync(string sessionid, string condition, int filetype, object userState) {
            if ((this.abnormalCardeventReportOperationCompleted == null)) {
                this.abnormalCardeventReportOperationCompleted = new System.Threading.SendOrPostCallback(this.OnabnormalCardeventReportOperationCompleted);
            }
            this.InvokeAsync("abnormalCardeventReport", new object[] {
                        sessionid,
                        condition,
                        filetype}, this.abnormalCardeventReportOperationCompleted, userState);
        }
        
        private void OnabnormalCardeventReportOperationCompleted(object arg) {
            if ((this.abnormalCardeventReportCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.abnormalCardeventReportCompleted(this, new abnormalCardeventReportCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("doorRelationReportReturn")]
        public string doorRelationReport(string sessionid, string condition, int filetype) {
            object[] results = this.Invoke("doorRelationReport", new object[] {
                        sessionid,
                        condition,
                        filetype});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void doorRelationReportAsync(string sessionid, string condition, int filetype) {
            this.doorRelationReportAsync(sessionid, condition, filetype, null);
        }
        
        /// <remarks/>
        public void doorRelationReportAsync(string sessionid, string condition, int filetype, object userState) {
            if ((this.doorRelationReportOperationCompleted == null)) {
                this.doorRelationReportOperationCompleted = new System.Threading.SendOrPostCallback(this.OndoorRelationReportOperationCompleted);
            }
            this.InvokeAsync("doorRelationReport", new object[] {
                        sessionid,
                        condition,
                        filetype}, this.doorRelationReportOperationCompleted, userState);
        }
        
        private void OndoorRelationReportOperationCompleted(object arg) {
            if ((this.doorRelationReportCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.doorRelationReportCompleted(this, new doorRelationReportCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("abnormalEmployeeeventReportReturn")]
        public string abnormalEmployeeeventReport(string sessionid, string condition, int filetype) {
            object[] results = this.Invoke("abnormalEmployeeeventReport", new object[] {
                        sessionid,
                        condition,
                        filetype});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void abnormalEmployeeeventReportAsync(string sessionid, string condition, int filetype) {
            this.abnormalEmployeeeventReportAsync(sessionid, condition, filetype, null);
        }
        
        /// <remarks/>
        public void abnormalEmployeeeventReportAsync(string sessionid, string condition, int filetype, object userState) {
            if ((this.abnormalEmployeeeventReportOperationCompleted == null)) {
                this.abnormalEmployeeeventReportOperationCompleted = new System.Threading.SendOrPostCallback(this.OnabnormalEmployeeeventReportOperationCompleted);
            }
            this.InvokeAsync("abnormalEmployeeeventReport", new object[] {
                        sessionid,
                        condition,
                        filetype}, this.abnormalEmployeeeventReportOperationCompleted, userState);
        }
        
        private void OnabnormalEmployeeeventReportOperationCompleted(object arg) {
            if ((this.abnormalEmployeeeventReportCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.abnormalEmployeeeventReportCompleted(this, new abnormalEmployeeeventReportCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("employeeAuthReportReturn")]
        public string employeeAuthReport(string sessionid, string condition, int filetype) {
            object[] results = this.Invoke("employeeAuthReport", new object[] {
                        sessionid,
                        condition,
                        filetype});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void employeeAuthReportAsync(string sessionid, string condition, int filetype) {
            this.employeeAuthReportAsync(sessionid, condition, filetype, null);
        }
        
        /// <remarks/>
        public void employeeAuthReportAsync(string sessionid, string condition, int filetype, object userState) {
            if ((this.employeeAuthReportOperationCompleted == null)) {
                this.employeeAuthReportOperationCompleted = new System.Threading.SendOrPostCallback(this.OnemployeeAuthReportOperationCompleted);
            }
            this.InvokeAsync("employeeAuthReport", new object[] {
                        sessionid,
                        condition,
                        filetype}, this.employeeAuthReportOperationCompleted, userState);
        }
        
        private void OnemployeeAuthReportOperationCompleted(object arg) {
            if ((this.employeeAuthReportCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.employeeAuthReportCompleted(this, new employeeAuthReportCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("doorReportReturn")]
        public string doorReport(string sessionid, string condition, int filetype) {
            object[] results = this.Invoke("doorReport", new object[] {
                        sessionid,
                        condition,
                        filetype});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void doorReportAsync(string sessionid, string condition, int filetype) {
            this.doorReportAsync(sessionid, condition, filetype, null);
        }
        
        /// <remarks/>
        public void doorReportAsync(string sessionid, string condition, int filetype, object userState) {
            if ((this.doorReportOperationCompleted == null)) {
                this.doorReportOperationCompleted = new System.Threading.SendOrPostCallback(this.OndoorReportOperationCompleted);
            }
            this.InvokeAsync("doorReport", new object[] {
                        sessionid,
                        condition,
                        filetype}, this.doorReportOperationCompleted, userState);
        }
        
        private void OndoorReportOperationCompleted(object arg) {
            if ((this.doorReportCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.doorReportCompleted(this, new doorReportCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("accessLabReportReturn")]
        public string accessLabReport(string sessionid, string condition, int filetype) {
            object[] results = this.Invoke("accessLabReport", new object[] {
                        sessionid,
                        condition,
                        filetype});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void accessLabReportAsync(string sessionid, string condition, int filetype) {
            this.accessLabReportAsync(sessionid, condition, filetype, null);
        }
        
        /// <remarks/>
        public void accessLabReportAsync(string sessionid, string condition, int filetype, object userState) {
            if ((this.accessLabReportOperationCompleted == null)) {
                this.accessLabReportOperationCompleted = new System.Threading.SendOrPostCallback(this.OnaccessLabReportOperationCompleted);
            }
            this.InvokeAsync("accessLabReport", new object[] {
                        sessionid,
                        condition,
                        filetype}, this.accessLabReportOperationCompleted, userState);
        }
        
        private void OnaccessLabReportOperationCompleted(object arg) {
            if ((this.accessLabReportCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.accessLabReportCompleted(this, new accessLabReportCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("employeeDoorsReporttReturn")]
        public string employeeDoorsReport(string sessionid, string condition, int filetype) {
            object[] results = this.Invoke("employeeDoorsReport", new object[] {
                        sessionid,
                        condition,
                        filetype});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void employeeDoorsReportAsync(string sessionid, string condition, int filetype) {
            this.employeeDoorsReportAsync(sessionid, condition, filetype, null);
        }
        
        /// <remarks/>
        public void employeeDoorsReportAsync(string sessionid, string condition, int filetype, object userState) {
            if ((this.employeeDoorsReportOperationCompleted == null)) {
                this.employeeDoorsReportOperationCompleted = new System.Threading.SendOrPostCallback(this.OnemployeeDoorsReportOperationCompleted);
            }
            this.InvokeAsync("employeeDoorsReport", new object[] {
                        sessionid,
                        condition,
                        filetype}, this.employeeDoorsReportOperationCompleted, userState);
        }
        
        private void OnemployeeDoorsReportOperationCompleted(object arg) {
            if ((this.employeeDoorsReportCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.employeeDoorsReportCompleted(this, new employeeDoorsReportCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("heartBeatReturn")]
        public string heartBeat(string sessionid) {
            object[] results = this.Invoke("heartBeat", new object[] {
                        sessionid});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void heartBeatAsync(string sessionid) {
            this.heartBeatAsync(sessionid, null);
        }
        
        /// <remarks/>
        public void heartBeatAsync(string sessionid, object userState) {
            if ((this.heartBeatOperationCompleted == null)) {
                this.heartBeatOperationCompleted = new System.Threading.SendOrPostCallback(this.OnheartBeatOperationCompleted);
            }
            this.InvokeAsync("heartBeat", new object[] {
                        sessionid}, this.heartBeatOperationCompleted, userState);
        }
        
        private void OnheartBeatOperationCompleted(object arg) {
            if ((this.heartBeatCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.heartBeatCompleted(this, new heartBeatCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        [System.Web.Services.Protocols.SoapRpcMethodAttribute("urn:ServiceAction", RequestNamespace="urn:wsdl", ResponseNamespace="urn:wsdl")]
        [return: System.Xml.Serialization.SoapElementAttribute("searchDoorsCardsReturn")]
        public string searchDoorsCards(string sessionid, string doors) {
            object[] results = this.Invoke("searchDoorsCards", new object[] {
                        sessionid,
                        doors});
            return ((string)(results[0]));
        }
        
        /// <remarks/>
        public void searchDoorsCardsAsync(string sessionid, string doors) {
            this.searchDoorsCardsAsync(sessionid, doors, null);
        }
        
        /// <remarks/>
        public void searchDoorsCardsAsync(string sessionid, string doors, object userState) {
            if ((this.searchDoorsCardsOperationCompleted == null)) {
                this.searchDoorsCardsOperationCompleted = new System.Threading.SendOrPostCallback(this.OnsearchDoorsCardsOperationCompleted);
            }
            this.InvokeAsync("searchDoorsCards", new object[] {
                        sessionid,
                        doors}, this.searchDoorsCardsOperationCompleted, userState);
        }
        
        private void OnsearchDoorsCardsOperationCompleted(object arg) {
            if ((this.searchDoorsCardsCompleted != null)) {
                System.Web.Services.Protocols.InvokeCompletedEventArgs invokeArgs = ((System.Web.Services.Protocols.InvokeCompletedEventArgs)(arg));
                this.searchDoorsCardsCompleted(this, new searchDoorsCardsCompletedEventArgs(invokeArgs.Results, invokeArgs.Error, invokeArgs.Cancelled, invokeArgs.UserState));
            }
        }
        
        /// <remarks/>
        public new void CancelAsync(object userState) {
            base.CancelAsync(userState);
        }
        
        private bool IsLocalFileSystemWebService(string url) {
            if (((url == null) 
                        || (url == string.Empty))) {
                return false;
            }
            System.Uri wsUri = new System.Uri(url);
            if (((wsUri.Port >= 1024) 
                        && (string.Compare(wsUri.Host, "localHost", System.StringComparison.OrdinalIgnoreCase) == 0))) {
                return true;
            }
            return false;
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void loginCompletedEventHandler(object sender, loginCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class loginCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal loginCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void getCtrsCompletedEventHandler(object sender, getCtrsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class getCtrsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal getCtrsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void getYaoCtrsCompletedEventHandler(object sender, getYaoCtrsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class getYaoCtrsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal getYaoCtrsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void getStatusCompletedEventHandler(object sender, getStatusCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class getStatusCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal getStatusCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void getCtrCardsCompletedEventHandler(object sender, getCtrCardsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class getCtrCardsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal getCtrCardsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void getDbCardsCompletedEventHandler(object sender, getDbCardsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class getDbCardsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal getDbCardsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void setDbCardsCompletedEventHandler(object sender, setDbCardsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class setDbCardsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal setDbCardsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void getDoorsCompletedEventHandler(object sender, getDoorsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class getDoorsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal getDoorsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void insertReserveCardsCompletedEventHandler(object sender, insertReserveCardsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class insertReserveCardsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal insertReserveCardsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void searchReserveCardsCompletedEventHandler(object sender, searchReserveCardsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class searchReserveCardsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal searchReserveCardsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void deleteReserveCardsCompletedEventHandler(object sender, deleteReserveCardsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class deleteReserveCardsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal deleteReserveCardsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void insertPublicDoorsCompletedEventHandler(object sender, insertPublicDoorsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class insertPublicDoorsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal insertPublicDoorsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void updatePublicDoorsCompletedEventHandler(object sender, updatePublicDoorsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class updatePublicDoorsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal updatePublicDoorsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void searchPublicDoorsCompletedEventHandler(object sender, searchPublicDoorsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class searchPublicDoorsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal searchPublicDoorsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void deletePublicDoorsCompletedEventHandler(object sender, deletePublicDoorsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class deletePublicDoorsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal deletePublicDoorsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void updateDoorsCompletedEventHandler(object sender, updateDoorsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class updateDoorsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal updateDoorsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void insertEmployeesCompletedEventHandler(object sender, insertEmployeesCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class insertEmployeesCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal insertEmployeesCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void deleteEmployeesCompletedEventHandler(object sender, deleteEmployeesCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class deleteEmployeesCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal deleteEmployeesCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void updateEmployeesCompletedEventHandler(object sender, updateEmployeesCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class updateEmployeesCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal updateEmployeesCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void searchEmployeesCompletedEventHandler(object sender, searchEmployeesCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class searchEmployeesCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal searchEmployeesCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void insertCardsCompletedEventHandler(object sender, insertCardsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class insertCardsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal insertCardsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void updateCardsCompletedEventHandler(object sender, updateCardsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class updateCardsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal updateCardsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void deleteCardsCompletedEventHandler(object sender, deleteCardsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class deleteCardsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal deleteCardsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void searchCardsCompletedEventHandler(object sender, searchCardsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class searchCardsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal searchCardsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void insertDeptsCompletedEventHandler(object sender, insertDeptsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class insertDeptsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal insertDeptsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void updateDeptsCompletedEventHandler(object sender, updateDeptsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class updateDeptsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal updateDeptsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void deleteDeptsCompletedEventHandler(object sender, deleteDeptsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class deleteDeptsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal deleteDeptsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void searchDeptsCompletedEventHandler(object sender, searchDeptsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class searchDeptsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal searchDeptsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void getCardsDoorsCompletedEventHandler(object sender, getCardsDoorsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class getCardsDoorsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal getCardsDoorsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void searchCardsDoorsCompletedEventHandler(object sender, searchCardsDoorsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class searchCardsDoorsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal searchCardsDoorsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void asyncSetCardsDoorsCompletedEventHandler(object sender, asyncSetCardsDoorsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class asyncSetCardsDoorsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal asyncSetCardsDoorsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void asyncSetCardsDoorsXCompletedEventHandler(object sender, asyncSetCardsDoorsXCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class asyncSetCardsDoorsXCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal asyncSetCardsDoorsXCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void setCardsDoorsCompletedEventHandler(object sender, setCardsDoorsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class setCardsDoorsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal setCardsDoorsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void setCardsDoorsSyncCompletedEventHandler(object sender, setCardsDoorsSyncCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class setCardsDoorsSyncCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal setCardsDoorsSyncCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void normalCardeventReportCompletedEventHandler(object sender, normalCardeventReportCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class normalCardeventReportCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal normalCardeventReportCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void abnormalCardeventReportCompletedEventHandler(object sender, abnormalCardeventReportCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class abnormalCardeventReportCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal abnormalCardeventReportCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void doorRelationReportCompletedEventHandler(object sender, doorRelationReportCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class doorRelationReportCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal doorRelationReportCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void abnormalEmployeeeventReportCompletedEventHandler(object sender, abnormalEmployeeeventReportCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class abnormalEmployeeeventReportCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal abnormalEmployeeeventReportCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void employeeAuthReportCompletedEventHandler(object sender, employeeAuthReportCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class employeeAuthReportCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal employeeAuthReportCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void doorReportCompletedEventHandler(object sender, doorReportCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class doorReportCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal doorReportCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void accessLabReportCompletedEventHandler(object sender, accessLabReportCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class accessLabReportCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal accessLabReportCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void employeeDoorsReportCompletedEventHandler(object sender, employeeDoorsReportCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class employeeDoorsReportCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal employeeDoorsReportCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void heartBeatCompletedEventHandler(object sender, heartBeatCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class heartBeatCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal heartBeatCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    public delegate void searchDoorsCardsCompletedEventHandler(object sender, searchDoorsCardsCompletedEventArgs e);
    
    /// <remarks/>
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Web.Services", "4.8.4084.0")]
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.ComponentModel.DesignerCategoryAttribute("code")]
    public partial class searchDoorsCardsCompletedEventArgs : System.ComponentModel.AsyncCompletedEventArgs {
        
        private object[] results;
        
        internal searchDoorsCardsCompletedEventArgs(object[] results, System.Exception exception, bool cancelled, object userState) : 
                base(exception, cancelled, userState) {
            this.results = results;
        }
        
        /// <remarks/>
        public string Result {
            get {
                this.RaiseExceptionIfNecessary();
                return ((string)(this.results[0]));
            }
        }
    }
}

#pragma warning restore 1591